const axios = require('axios');

/**
 * Service for handling mosque location detection and nearby mosque finding
 */
class MosqueService {
  constructor() {
    this.ipApiUrl = 'http://ip-api.com/json';
    this.googlePlacesApiUrl = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
    this.googleStaticMapsUrl = 'https://maps.googleapis.com/maps/api/staticmap';
    this.overpassApiUrl = 'https://overpass-api.de/api/interpreter';
  }

  /**
   * Get user location from IP address using ip-api.com (free service)
   * @param {string} ip - Client IP address
   * @returns {Promise<Object>} Location data with lat, lng, city, country
   */
  async getLocationFromIP(ip) {
    try {
      // Handle localhost/development scenarios
      if (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost') {
        // Return a default location (e.g., New York City) for development
        return {
          lat: 40.7128,
          lng: -74.0060,
          city: 'New York',
          country: 'United States',
          regionName: 'New York',
          zip: '10001',
          status: 'success'
        };
      }

      const response = await axios.get(`${this.ipApiUrl}/${ip}`, {
        timeout: 10000,
        params: {
          fields: 'status,message,country,regionName,city,zip,lat,lon,query'
        }
      });

      const data = response.data;

      if (data.status === 'fail') {
        throw new Error(data.message || 'Failed to get location from IP');
      }

      return {
        lat: data.lat,
        lng: data.lon,
        city: data.city,
        country: data.country,
        regionName: data.regionName,
        zip: data.zip,
        status: 'success'
      };
    } catch (error) {
      console.error('Error getting location from IP:', error.message);
      throw new Error('Unable to detect location from IP address');
    }
  }

  /**
   * Find nearby mosques using Google Places API
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} radius - Search radius in meters (default: 5000)
   * @returns {Promise<Array>} Array of nearby mosques
   */
  async findNearbyMosquesGoogle(lat, lng, radius = 5000) {
    try {
      const apiKey = process.env.GOOGLE_PLACES_API_KEY;
      if (!apiKey) {
        throw new Error('Google Places API key not configured');
      }

      const response = await axios.get(this.googlePlacesApiUrl, {
        timeout: 15000,
        params: {
          location: `${lat},${lng}`,
          radius: radius,
          type: 'mosque',
          key: apiKey
        }
      });

      const places = response.data.results || [];

      return places.map(place => ({
        name: place.name,
        address: place.vicinity || place.formatted_address || 'Address not available',
        coordinates: {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        },
        rating: place.rating || null,
        placeId: place.place_id,
        openNow: place.opening_hours?.open_now || null,
        priceLevel: place.price_level || null
      }));
    } catch (error) {
      console.error('Error finding mosques with Google Places:', error.message);
      throw new Error('Unable to find nearby mosques using Google Places API');
    }
  }

  /**
   * Find nearby mosques using OpenStreetMap Overpass API (free alternative)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} radius - Search radius in meters (default: 5000)
   * @returns {Promise<Array>} Array of nearby mosques
   */
  async findNearbyMosquesOSM(lat, lng, radius = 5000) {
    try {
      // Convert radius from meters to degrees (approximate)
      const radiusInDegrees = radius / 111320;

      const overpassQuery = `
        [out:json][timeout:25];
        (
          node["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
          way["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
          relation["amenity"="place_of_worship"]["religion"="muslim"](around:${radius},${lat},${lng});
        );
        out center;
      `;

      const response = await axios.post(this.overpassApiUrl, overpassQuery, {
        timeout: 30000,
        headers: {
          'Content-Type': 'text/plain'
        }
      });

      const data = response.data;
      const elements = data.elements || [];

      return elements.map(element => {
        const tags = element.tags || {};
        const coordinates = element.center || { lat: element.lat, lon: element.lon };

        return {
          name: tags.name || tags['name:en'] || tags['name:ar'] || 'Unnamed Mosque',
          address: this.formatOSMAddress(tags),
          coordinates: {
            lat: coordinates.lat,
            lng: coordinates.lon
          },
          denomination: tags.denomination || null,
          website: tags.website || null,
          phone: tags.phone || null,
          osmId: element.id,
          osmType: element.type
        };
      });
    } catch (error) {
      console.error('Error finding mosques with OSM:', error.message);
      throw new Error('Unable to find nearby mosques using OpenStreetMap');
    }
  }

  /**
   * Format OSM address from tags
   * @param {Object} tags - OSM tags
   * @returns {string} Formatted address
   */
  formatOSMAddress(tags) {
    const addressParts = [];
    
    if (tags['addr:housenumber']) addressParts.push(tags['addr:housenumber']);
    if (tags['addr:street']) addressParts.push(tags['addr:street']);
    if (tags['addr:city']) addressParts.push(tags['addr:city']);
    if (tags['addr:state']) addressParts.push(tags['addr:state']);
    if (tags['addr:postcode']) addressParts.push(tags['addr:postcode']);
    if (tags['addr:country']) addressParts.push(tags['addr:country']);

    return addressParts.length > 0 ? addressParts.join(', ') : 'Address not available';
  }

  /**
   * Generate Google Static Maps URL with location and mosque markers
   * @param {number} userLat - User latitude
   * @param {number} userLng - User longitude
   * @param {Array} mosques - Array of mosque objects
   * @param {Object} options - Map options (size, zoom, etc.)
   * @returns {string} Static map image URL
   */
  generateStaticMapUrl(userLat, userLng, mosques, options = {}) {
    try {
      const apiKey = process.env.GOOGLE_MAPS_API_KEY || process.env.GOOGLE_PLACES_API_KEY;
      if (!apiKey) {
        return null; // Return null if no API key available
      }

      const {
        size = '600x400',
        zoom = 13,
        maptype = 'roadmap'
      } = options;

      const params = new URLSearchParams({
        size,
        zoom,
        maptype,
        key: apiKey
      });

      // Add user location marker (red)
      params.append('markers', `color:red|label:U|${userLat},${userLng}`);

      // Add mosque markers (blue)
      mosques.slice(0, 10).forEach((mosque, index) => { // Limit to 10 markers to avoid URL length issues
        const label = String.fromCharCode(65 + index); // A, B, C, etc.
        params.append('markers', `color:blue|label:${label}|${mosque.coordinates.lat},${mosque.coordinates.lng}`);
      });

      return `${this.googleStaticMapsUrl}?${params.toString()}`;
    } catch (error) {
      console.error('Error generating static map URL:', error.message);
      return null;
    }
  }

  /**
   * Main method to find nearby mosques with fallback options
   * @param {string} ip - Client IP address
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Complete response with location, mosques, and map
   */
  async findNearbyMosques(ip, options = {}) {
    const { radius = 5000, useGoogle = true } = options;

    try {
      // Step 1: Get user location from IP
      const location = await this.getLocationFromIP(ip);

      // Step 2: Find nearby mosques
      let mosques = [];
      let dataSource = 'unknown';

      if (useGoogle && process.env.GOOGLE_PLACES_API_KEY) {
        try {
          mosques = await this.findNearbyMosquesGoogle(location.lat, location.lng, radius);
          dataSource = 'google_places';
        } catch (error) {
          console.warn('Google Places API failed, falling back to OSM:', error.message);
          mosques = await this.findNearbyMosquesOSM(location.lat, location.lng, radius);
          dataSource = 'openstreetmap';
        }
      } else {
        mosques = await this.findNearbyMosquesOSM(location.lat, location.lng, radius);
        dataSource = 'openstreetmap';
      }

      // Step 3: Generate static map URL
      const mapImageUrl = this.generateStaticMapUrl(location.lat, location.lng, mosques);

      return {
        success: true,
        location: {
          latitude: location.lat,
          longitude: location.lng,
          city: location.city,
          country: location.country,
          region: location.regionName
        },
        mosques: {
          count: mosques.length,
          data: mosques,
          dataSource,
          searchRadius: radius
        },
        mapImageUrl,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error in findNearbyMosques:', error.message);
      throw error;
    }
  }
}

module.exports = new MosqueService();
