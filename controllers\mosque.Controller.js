const mosqueService = require('../utils/mosqueService');

/**
 * Controller for mosque-related endpoints
 */
class MosqueController {
  /**
   * Find nearby mosques based on client IP location
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async findNearbyMosques(req, res) {
    try {
      // Extract client IP address
      const clientIP = MosqueController.prototype.getClientIP(req);

      // Extract query parameters
      const {
        radius = 5000,
        includeMap = 'true'
      } = req.query;

      // Validate radius
      const searchRadius = parseInt(radius);
      if (isNaN(searchRadius) || searchRadius < 100 || searchRadius > 50000) {
        return res.status(400).json({
          success: false,
          error: 'Invalid radius. Must be between 100 and 50000 meters.',
          code: 'INVALID_RADIUS'
        });
      }

      // Convert string boolean parameters
      const shouldIncludeMap = includeMap.toLowerCase() === 'true';

      console.log(`Finding mosques for IP: ${clientIP}, radius: ${searchRadius}m`);

      // Call the mosque service (now uses only free services)
      const result = await mosqueService.findNearbyMosques(clientIP, {
        radius: searchRadius
      });

      // Remove map URL if not requested
      if (!shouldIncludeMap) {
        delete result.mapImageUrl;
      }

      // Add request metadata
      result.requestInfo = {
        clientIP: clientIP,
        searchRadius: searchRadius,
        dataSource: result.mosques.dataSource,
        includeMap: shouldIncludeMap,
        freeServicesOnly: true
      };

      res.status(200).json(result);

    } catch (error) {
      console.error('Error in findNearbyMosques controller:', error.message);

      // Handle specific error types
      if (error.message.includes('Unable to detect location')) {
        return res.status(400).json({
          success: false,
          error: 'Unable to detect your location from IP address. Please try again.',
          code: 'LOCATION_DETECTION_FAILED'
        });
      }

      if (error.message.includes('OpenStreetMap') || error.message.includes('Overpass')) {
        return res.status(500).json({
          success: false,
          error: 'Mosque data service temporarily unavailable. Please try again later.',
          code: 'SERVICE_UNAVAILABLE'
        });
      }

      if (error.message.includes('Unable to find nearby mosques')) {
        return res.status(404).json({
          success: false,
          error: 'No mosques found in your area. Try increasing the search radius.',
          code: 'NO_MOSQUES_FOUND'
        });
      }

      // Generic error response
      res.status(500).json({
        success: false,
        error: 'An error occurred while finding nearby mosques. Please try again.',
        code: 'INTERNAL_SERVER_ERROR'
      });
    }
  }

  /**
   * Get health status of mosque service
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getServiceHealth(req, res) {
    try {
      const health = {
        success: true,
        service: 'Mosque Finder API',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        features: {
          ipGeolocation: 'ip-api.com (free)',
          mosqueData: {
            openstreetmap: true,
            dataSource: 'OpenStreetMap Overpass API (free)'
          },
          staticMaps: 'OpenStreetMap embed (free)',
          publicIpDetection: 'ipify.org (free)'
        },
        endpoints: [
          {
            path: '/api/mosques/nearby',
            method: 'GET',
            description: 'Find nearby mosques based on IP location'
          },
          {
            path: '/api/mosques/health',
            method: 'GET',
            description: 'Service health check'
          }
        ]
      };

      res.status(200).json(health);
    } catch (error) {
      console.error('Error in getServiceHealth:', error.message);
      res.status(500).json({
        success: false,
        service: 'Mosque Finder API',
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get API documentation/usage information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getApiDocs(req, res) {
    try {
      const docs = {
        success: true,
        api: 'Mosque Finder API',
        version: '1.0.0',
        description: 'Backend API for finding nearby mosques based on IP geolocation',
        baseUrl: `${req.protocol}://${req.get('host')}`,
        endpoints: {
          findNearbyMosques: {
            path: '/api/mosques/nearby',
            method: 'GET',
            description: 'Find nearby mosques based on client IP location',
            parameters: {
              radius: {
                type: 'integer',
                default: 5000,
                min: 100,
                max: 50000,
                description: 'Search radius in meters'
              },
              useGoogle: {
                type: 'boolean',
                default: true,
                description: 'Use Google Places API (falls back to OpenStreetMap if false or unavailable)'
              },
              includeMap: {
                type: 'boolean',
                default: true,
                description: 'Include static map image URL in response'
              }
            },
            example: '/api/mosques/nearby?radius=3000&useGoogle=true&includeMap=true'
          },
          health: {
            path: '/api/mosques/health',
            method: 'GET',
            description: 'Service health check and feature availability'
          },
          docs: {
            path: '/api/mosques/docs',
            method: 'GET',
            description: 'API documentation (this endpoint)'
          }
        },
        responseFormat: {
          success: 'boolean',
          location: {
            latitude: 'number',
            longitude: 'number',
            city: 'string',
            country: 'string',
            region: 'string'
          },
          mosques: {
            count: 'number',
            data: 'array of mosque objects',
            dataSource: 'string (google_places or openstreetmap)',
            searchRadius: 'number'
          },
          mapImageUrl: 'string (optional)',
          timestamp: 'ISO date string'
        },
        errorCodes: {
          INVALID_RADIUS: 'Radius parameter is invalid',
          LOCATION_DETECTION_FAILED: 'Unable to detect location from IP',
          SERVICE_UNAVAILABLE: 'External service temporarily unavailable',
          NO_MOSQUES_FOUND: 'No mosques found in the specified area',
          INTERNAL_SERVER_ERROR: 'Generic server error'
        }
      };

      res.status(200).json(docs);
    } catch (error) {
      console.error('Error in getApiDocs:', error.message);
      res.status(500).json({
        success: false,
        error: 'Unable to load API documentation'
      });
    }
  }

  /**
   * Extract client IP address from request
   * @param {Object} req - Express request object
   * @returns {string} Client IP address
   */
  getClientIP(req) {
    // Check various headers for the real IP address
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const cfConnectingIP = req.headers['cf-connecting-ip']; // Cloudflare

    if (forwarded) {
      // x-forwarded-for can contain multiple IPs, take the first one
      return forwarded.split(',')[0].trim();
    }

    if (realIP) {
      return realIP;
    }

    if (cfConnectingIP) {
      return cfConnectingIP;
    }

    // Fallback to connection remote address
    return req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           '127.0.0.1';
  }
}

module.exports = new MosqueController();
