# MongoDB Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/
DATA_BASE_NAME=your_database_name

# Email Configuration
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password

# Google API Keys for Mosque Finder
# Get these from: https://console.cloud.google.com/
# Enable Places API and Maps Static API
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Alternative IP Geolocation APIs (optional)
# IPSTACK_API_KEY=your_ipstack_api_key_here
# IPGEOLOCATION_API_KEY=your_ipgeolocation_api_key_here

# Server Configuration
PORT=5000
