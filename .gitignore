# Node modules
node_modules/

# Environment variables
.env

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# Optional: local development files
.vscode/
*.local

# Vercel output (optional, if generated locally)
.vercel/

# Coverage reports
coverage/

# npm lock file (optional if using yarn)
package-lock.json

# Debug files
*.swp
*.swo

.early.coverage

#CHAT_SYSTEM_README.md
.CHAT_SYSTEM_README.md